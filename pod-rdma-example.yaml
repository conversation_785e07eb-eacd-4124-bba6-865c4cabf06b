# 使用节点亲和性来选择正确的 RDMA 接口
apiVersion: v1
kind: Pod
metadata:
  name: rdma-pod-example
  annotations:
    # 对于 ens3f0 节点
    k8s.v1.cni.cncf.io/networks: |
      [{
        "name": "rdma-macvlan",
        "interface": "net1",
        "mac": "macvlan",
        "master": "ens3f0"
      }]
spec:
  nodeSelector:
    rdma-interface: "ens3f0"
  containers:
  - name: app
    image: your-app:latest
---
apiVersion: v1
kind: Pod
metadata:
  name: rdma-pod-example-2
  annotations:
    # 对于 ens4f0 节点
    k8s.v1.cni.cncf.io/networks: |
      [{
        "name": "rdma-macvlan",
        "interface": "net1",
        "mac": "macvlan",
        "master": "ens4f0"
      }]
spec:
  nodeSelector:
    rdma-interface: "ens4f0"
  containers:
  - name: app
    image: your-app:latest