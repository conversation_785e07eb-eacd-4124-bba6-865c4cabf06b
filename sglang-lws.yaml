apiVersion: leaderworkerset.x-k8s.io/v1
kind: LeaderWorkerSet
metadata:
  name: sglang
  namespace: sglang
spec:
  replicas: 1
  leaderWorkerTemplate:
    size: 2
    restartPolicy: RecreateGroupOnPodRestart
    leaderTemplate:
      metadata:
        labels:
          role: leader
      spec:
        containers:
          - name: sglang-leader
            image: harbor.caih.local/caihcloud/sglang:v0.4.6.post1-cu121 #不能用太高cuda版本的，nccl版本太高不适配，分布式推理貌似只支持2.11以下版本的
            env:
              - name: HUGGING_FACE_HUB_TOKEN
                value: $HUGGING_FACE_HUB_TOKEN
              - name: NCCL_DEBUG
                value: TRACE
              - name: NCCL_IB_DISABLE
                value: '1'
              - name: GLOO_SOCKET_IFNAME
                value: eth0
              - name: NCCL_SOCKET_IFNAME
                value: eth0
              - name: TP_SOCKET_IFNAME
                value: eth0
              - name: LWS_WORKER_INDEX
                valueFrom:
                  fieldRef:
                    fieldPath: >-
                      metadata.labels['leaderworkerset.sigs.k8s.io/worker-index']
            command:
              - python3
              - -m
              - sglang.launch_server
              - --model-path
              - /app/data/model/Qwen3-0.6B
              - --tp
              - "4" # Size of Tensor Parallelism
              - --dist-init-addr
              - $(LWS_LEADER_ADDRESS):20000
              - --nnodes
              - $(LWS_GROUP_SIZE)
              - --node-rank
              - $(LWS_WORKER_INDEX)
              - --trust-remote-code
              - --served-model-name
              - Qwen/Qwen3-0.6B
              - --served-model-name
              - Qwen/Qwen3-0.6B
              - --host
              - "0.0.0.0"
              - --port
              - "40000"
            resources:
              limits:
                nvidia.com/gpu: "2"
            ports:
              - containerPort: 40000
            readinessProbe:
              tcpSocket:
                port: 40000
              initialDelaySeconds: 15
              periodSeconds: 10
            volumeMounts:
              - mountPath: /dev/shm
                name: dshm
              - mountPath: /app/data/model
                name: model-volume
        volumes:
          - name: dshm
            emptyDir:
              medium: Memory
          - name: model-volume
            hostPath:
              path: /app/data/model
              type: Directory
    workerTemplate:
      spec:
        containers:
          - name: sglang-worker
            image: harbor.caih.local/caihcloud/sglang:v0.4.6.post1-cu121 #不能用太高cuda版本的，nccl版本太高不适配，分布式推理貌似只支持2.11以下版本的
            env:
            - name: HUGGING_FACE_HUB_TOKEN
              value: $HUGGING_FACE_HUB_TOKEN
            - name: NCCL_DEBUG
              value: TRACE
            - name: NCCL_IB_DISABLE
              value: '1'
            - name: GLOO_SOCKET_IFNAME
              value: eth0
            - name: NCCL_SOCKET_IFNAME
              value: eth0
            - name: TP_SOCKET_IFNAME
              value: eth0
            - name: LWS_WORKER_INDEX
              valueFrom:
                fieldRef:
                  fieldPath: >-
                    metadata.labels['leaderworkerset.sigs.k8s.io/worker-index']
            command:
              - python3
              - -m
              - sglang.launch_server
              - --model-path
              - /app/data/model/Qwen3-0.6B
              - --tp
              - "4" # Size of Tensor Parallelism
              - --dist-init-addr
              - $(LWS_LEADER_ADDRESS):20000
              - --nnodes
              - $(LWS_GROUP_SIZE)
              - --node-rank
              - $(LWS_WORKER_INDEX)
              - --trust-remote-code
              - --served-model-name
              - Qwen/Qwen3-0.6B
            resources:
              limits:
                nvidia.com/gpu: "2"
            volumeMounts:
              - mountPath: /dev/shm
                name: dshm
              - mountPath: /app/data/model
                name: model-volume
        volumes:
          - name: dshm
            emptyDir:
              medium: Memory
          - name: model-volume
            hostPath:
              path: /app/data/model
              type: Directory
---
apiVersion: v1
kind: Service
metadata:
  name: sglang-leader
  namespace: sglang
spec:
  selector:
    leaderworkerset.sigs.k8s.io/name: sglang
    role: leader
  ports:
    - protocol: TCP
      port: 40000
      targetPort: 40000