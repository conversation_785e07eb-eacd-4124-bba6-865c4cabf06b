# LLM 服务部署配置

本项目包含基于 Kubernetes 的高性能 LLM（大语言模型）服务部署配置文件，支持 vLLM 和 SGLang 两种推理框架的单机和多机分布式部署。

## 项目概述

本项目提供了在 GPU 集群上部署高吞吐量 LLM 服务的完整 Kubernetes 配置，支持：

- **vLLM**: 基于 Ray 的高吞吐量 LLM 服务框架
- **SGLang**: 支持结构化生成的语言服务框架
- **单机部署**: 使用标准 Deployment 进行单节点推理
- **多机分布式部署**: 使用 LeaderWorkerSet (LWS) 进行分布式推理

## 部署文件说明

### vLLM 部署

#### 1. `vllm-deployment.yaml` - 单机部署
- **用途**: 单机单卡或单机多卡推理
- **配置**: 
  - 使用 Deployment 资源
  - 分配 2 个 GPU (nvidia.com/gpu: "2")
  - tensor-parallel-size=2 (张量并行)
  - 端口: 8080 (OpenAI 兼容 API)
- **适用场景**: 开发测试、小型模型推理

#### 2. `vllm-lws.yaml` - 多机分布式部署
- **用途**: 多机多卡分布式推理
- **配置**:
  - 使用 LeaderWorkerSet 资源
  - 每个节点 2 个 GPU
  - tensor-parallel-size=2, pipeline-parallel-size=2
  - Leader-Worker 架构实现分布式推理
- **适用场景**: 大型模型、高并发生产环境

### SGLang 部署

#### 1. `sglang-deployment.yaml` - 单机部署
- **用途**: 单机推理
- **配置**:
  - 使用 Deployment 资源
  - 分配 1 个 GPU
  - tp=1 (张量并行度为1)
  - 端口: 8080
- **适用场景**: 开发测试、结构化生成任务

#### 2. `sglang-lws.yaml` - 多机分布式部署
- **用途**: 多机多卡分布式推理
- **配置**:
  - 使用 LeaderWorkerSet 资源
  - 每个节点 2 个 GPU
  - tp=4 (跨节点张量并行)
  - 端口: 40000
  - 网络优化配置 (NCCL, GLOO)
- **适用场景**: 大型模型分布式推理

## 重要版本兼容性说明

### vLLM 版本限制
- **当前版本**: `v0.8.5.post1`
- **重要说明**: vLLM 0.9 以上版本的 NCCL 版本过新，与 Qwen 系列模型的多机多卡部署不兼容
- **建议**: 生产环境中必须使用 0.8.5 版本以确保 Qwen 模型的分布式推理稳定性

### SGLang 版本限制
- **当前版本**: `v0.4.6.post1-cu121`
- **CUDA 版本**: 12.1
- **重要说明**: 
  - 更新版本的 CUDA 和 NCCL 与现有 A10 GPU 环境不兼容
  - Qwen3 模型对高版本 CUDA 支持有限
- **建议**: 在 A10 环境中必须使用 CUDA 12.1 版本

### 模型兼容性说明
- **Qwen 0.6B 模型**: 不支持 FP8 量化版本
- **硬件要求**: FP8 版本在 A10 GPU 上无法正常运行
- **建议**: 使用标准精度版本确保在 A10 环境中的兼容性

## 部署命令

### vLLM 部署
```bash
# 单机部署
kubectl apply -f vllm-deployment.yaml

# 多机分布式部署
kubectl apply -f vllm-lws.yaml

# 检查状态
kubectl get pods -n vllm
kubectl get svc -n vllm
```

### SGLang 部署
```bash
# 单机部署
kubectl apply -f sglang-deployment.yaml

# 多机分布式部署
kubectl apply -f sglang-lws.yaml

# 检查状态
kubectl get pods -n sglang
kubectl get svc -n sglang
```

## 访问服务

### vLLM 服务
- **API 端口**: 8080
- **协议**: OpenAI 兼容 API
- **访问方式**: 
  ```bash
  kubectl port-forward svc/vllm-service 8080:80 -n vllm  # 单机
  kubectl port-forward svc/vllm-leader 8080:80 -n vllm   # 多机
  ```

### SGLang 服务
- **API 端口**: 40000 (多机) / 8080 (单机)
- **协议**: SGLang 原生 API
- **访问方式**:
  ```bash
  kubectl port-forward svc/sglang-service-single-node 8080:80 -n sglang  # 单机
  kubectl port-forward svc/sglang-leader 40000:40000 -n sglang            # 多机
  ```

## 资源配置

### 硬件要求
- **GPU**: 支持 CUDA 的 NVIDIA GPU (建议 A10 或更高)
- **内存**: 每个 Pod 32-128GB
- **存储**: 100GB 临时存储用于模型缓存
- **网络**: 多机部署需要高速网络互联

### 存储配置
- **模型路径**: `/app/data/model` (Host Path)
- **共享内存**: `/dev/shm` 4GB (用于进程间通信)
- **模型文件**: 需要预先下载到各节点的 `/app/data/model` 目录

## 注意事项

1. **节点选择**: 所有 Pod 将调度到标记为 `gpu: "on"` 的节点
2. **模型预加载**: 确保模型文件在所有 GPU 节点上可用
3. **网络配置**: 多机部署需要配置 NCCL 网络参数
4. **版本兼容**: 严格按照指定版本部署，避免兼容性问题
5. **资源监控**: 建议监控 GPU 使用率和内存占用

## 故障排除

### 常见问题
1. **Pod 启动失败**: 检查 GPU 资源和模型文件路径
2. **分布式连接失败**: 检查网络配置和 NCCL 环境变量
3. **模型加载错误**: 确认模型版本与框架版本兼容性
4. **性能问题**: 调整并行度参数和共享内存大小

### 日志查看
```bash
# 查看 vLLM 日志
kubectl logs -l app=vllm -n vllm

# 查看 SGLang 日志
kubectl logs -l role=leader -n sglang
kubectl logs -l leaderworkerset.sigs.k8s.io/name=sglang -n sglang
```