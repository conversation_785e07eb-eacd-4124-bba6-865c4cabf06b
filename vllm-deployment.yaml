apiVersion: apps/v1
kind: Deployment
metadata:
  name: vllm-single-node
  namespace: vllm
  labels:
    app: vllm
spec:
  replicas: 1
  selector:
    matchLabels:
      app: vllm
  template:
    metadata:
      labels:
        app: vllm
    spec:
      nodeSelector:
        gpu: "on"
      containers:
        - name: vllm
          image: harbor.caih.local/caihcloud/vllm-openai:v0.8.5.post1
          command:
            - python3
            - -m
            - vllm.entrypoints.openai.api_server
          args:
            - --port=8080
            - --model=/app/data/model/Qwen3-0.6B
            - --tensor-parallel-size=2
          resources:
            limits:
              nvidia.com/gpu: "2"
              memory: 128Gi
              ephemeral-storage: 100Gi
            requests:
              ephemeral-storage: 100Gi
              cpu: 16
          ports:
            - containerPort: 8080
          readinessProbe:
            tcpSocket:
              port: 8080
            initialDelaySeconds: 15
            periodSeconds: 10
          volumeMounts:
            - mountPath: /dev/shm
              name: dshm
            - mountPath: /app/data/model
              name: model-volume
      volumes:
      - name: dshm
        emptyDir:
          medium: Memory
          sizeLimit: 4Gi
      - name: model-volume
        hostPath:
          path: /app/data/model
          type: Directory
---
apiVersion: v1
kind: Service
metadata:
  name: vllm-service
  namespace: vllm
  labels:
    app: vllm
spec:
  ports:
    - name: http
      port: 80
      protocol: TCP
      targetPort: 8080
  selector:
    app: vllm
  type: NodePort