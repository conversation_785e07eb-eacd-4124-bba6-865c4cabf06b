apiVersion: leaderworkerset.x-k8s.io/v1
kind: LeaderWorkerSet
metadata:
  name: sglang-rdma
  namespace: sglang
spec:
  replicas: 1
  leaderWorkerTemplate:
    size: 2
    restartPolicy: RecreateGroupOnPodRestart
    leaderTemplate:
      metadata:
        labels:
          role: leader
        annotations:
          k8s.v1.cni.cncf.io/networks: default/macvlan-conf-ens3f0
      spec:
        containers:
          - name: sglang-leader
            image: harbor.caih.local/caihcloud/sglang:v0.4.6.post1-cu121 #不能用太高cuda版本的，nccl版本太高不适配，分布式推理貌似只支持2.11以下版本的
            env:
              - name: HUGGING_FACE_HUB_TOKEN
                value: $HUGGING_FACE_HUB_TOKEN
              # RDMA/InfiniBand configuration
              - name: NCCL_IB_DISABLE
                value: '0'  # 启用 InfiniBand
              - name: NCCL_SOCKET_IFNAME
                value: net1  # 使用 RoCE 网络接口
              - name: GLOO_SOCKET_IFNAME
                value: net1
              - name: TP_SOCKET_IFNAME
                value: net1
              - name: NCCL_DEBUG
                value: INFO  # 可以设置为 TRACE 进行详细调试
              - name: LWS_WORKER_INDEX
                valueFrom:
                  fieldRef:
                    fieldPath: >-
                      metadata.labels['leaderworkerset.sigs.k8s.io/worker-index']
            command:
              - /bin/bash
              - -c
              - |
                export NCCL_IB_GID_INDEX=$(/host/rdma/show_gids | awk '$5 != "" && $6 == "v2" {print $3}')
                python3 -m sglang.launch_server --model-path /app/data/model/Qwen3-0.6B --tp "4" --dist-init-addr $(LWS_LEADER_ADDRESS):20000 --nnodes $(LWS_GROUP_SIZE) --node-rank $(LWS_WORKER_INDEX) --trust-remote-code --served-model-name Qwen/Qwen3-0.6B --served-model-name Qwen/Qwen3-0.6B --host "0.0.0.0" --port "40000"
            resources:
              limits:
                nvidia.com/gpu: "2"
                rdma/hca_shared_devices_ens3f0: '1'
            ports:
              - containerPort: 40000
            readinessProbe:
              tcpSocket:
                port: 40000
              initialDelaySeconds: 15
              periodSeconds: 10
            volumeMounts:
              - mountPath: /dev/shm
                name: dshm
              - mountPath: /app/data/model
                name: model-volume
              - mountPath: /host/rdma
                name: bin
            securityContext:
              capabilities:
                add:
                  - IPC_LOCK  # RDMA 需要的权限
        volumes:
          - name: dshm
            emptyDir:
              medium: Memory
          - name: model-volume
            hostPath:
              path: /app/data/model
              type: Directory
          - name: bin
            hostPath:
              path: /sbin
              type: Directory
    workerTemplate:
      metadata:
        annotations:
          k8s.v1.cni.cncf.io/networks: default/macvlan-conf-ens4f0
      spec:
        containers:
          - name: sglang-worker
            image: harbor.caih.local/caihcloud/sglang:v0.4.6.post1-cu121 #不能用太高cuda版本的，nccl版本太高不适配，分布式推理貌似只支持2.11以下版本的
            env:
            - name: HUGGING_FACE_HUB_TOKEN
              value: $HUGGING_FACE_HUB_TOKEN
            # RDMA/InfiniBand configuration
            - name: NCCL_IB_DISABLE
              value: '0'  # 启用 InfiniBand
            - name: NCCL_SOCKET_IFNAME
              value: net1  # 使用 RoCE 网络接口
            - name: GLOO_SOCKET_IFNAME
              value: net1
            - name: TP_SOCKET_IFNAME
              value: net1
            - name: NCCL_DEBUG
              value: INFO  # 可以设置为 TRACE 进行详细调试
            - name: LWS_WORKER_INDEX
              valueFrom:
                fieldRef:
                  fieldPath: >-
                    metadata.labels['leaderworkerset.sigs.k8s.io/worker-index']
            command:
              - /bin/bash
              - -c
              - |
                export NCCL_IB_GID_INDEX=$(/host/rdma/show_gids | awk '$5 != "" && $6 == "v2" {print $3}')
                python3 -m sglang.launch_server --model-path /app/data/model/Qwen3-0.6B --tp "4" --dist-init-addr $(LWS_LEADER_ADDRESS):20000 --nnodes $(LWS_GROUP_SIZE) --node-rank $(LWS_WORKER_INDEX) --trust-remote-code --served-model-name Qwen/Qwen3-0.6B
            resources:
              limits:
                nvidia.com/gpu: "2"
                rdma/hca_shared_devices_ens4f0: '1'
            volumeMounts:
              - mountPath: /dev/shm
                name: dshm
              - mountPath: /app/data/model
                name: model-volume
              - mountPath: /host/rdma
                name: bin
            securityContext:
              capabilities:
                add:
                  - IPC_LOCK  # RDMA 需要的权限
        volumes:
          - name: dshm
            emptyDir:
              medium: Memory
          - name: model-volume
            hostPath:
              path: /app/data/model
              type: Directory
          - name: bin
            hostPath:
              path: /sbin
              type: Directory
---
apiVersion: v1
kind: Service
metadata:
  name: sglang-rdma-leader
  namespace: sglang
spec:
  selector:
    leaderworkerset.sigs.k8s.io/name: sglang-rdma
    role: leader
  ports:
    - protocol: TCP
      port: 40000
      targetPort: 40000