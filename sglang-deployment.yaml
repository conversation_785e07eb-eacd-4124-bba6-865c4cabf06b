apiVersion: apps/v1
kind: Deployment
metadata:
  name: sglang-single-node
  namespace: sglang
  labels:
    app: sglang-single-node
spec:
  replicas: 1
  selector:
    matchLabels:
      app: sglang-single-node
  template:
    metadata:
      labels:
        app: sglang-single-node
    spec:
      nodeSelector:
        gpu: "on"
      containers:
        - name: sglang
          image: harbor.caih.local/caihcloud/sglang:v0.4.6.post1-cu121
          command:
            - python3
            - -m
            - sglang.launch_server
          args:
            - --host=0.0.0.0
            - --port=8080
            - --model-path=/app/data/model/Qwen3-0.6B
            - --tp=1
            - --served-model-name=Qwen/Qwen3-0.6B
          resources:
            limits:
              nvidia.com/gpu: "1"
              memory: 64Gi
              ephemeral-storage: 100Gi
            requests:
              ephemeral-storage: 100Gi
              cpu: 8
          ports:
            - containerPort: 8080
          readinessProbe:
            tcpSocket:
              port: 8080
            initialDelaySeconds: 15
            periodSeconds: 10
          volumeMounts:
            - mountPath: /dev/shm
              name: dshm
            - mountPath: /app/data/model
              name: model-volume
      volumes:
      - name: dshm
        emptyDir:
          medium: Memory
          sizeLimit: 4Gi
      - name: model-volume
        hostPath:
          path: /app/data/model
          type: Directory
---
apiVersion: v1
kind: Service
metadata:
  name: sglang-service-single-node
  namespace: sglang
  labels:
    app: sglang-single-node
spec:
  ports:
    - name: http
      port: 80
      protocol: TCP
      targetPort: 8080
  selector:
    app: sglang-single-node
  type: NodePort