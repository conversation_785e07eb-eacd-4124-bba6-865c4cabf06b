apiVersion: leaderworkerset.x-k8s.io/v1
kind: LeaderWorkerSet
metadata:
  name: vllm-rdma
  namespace: vllm
spec:
  leaderWorkerTemplate:
    leaderTemplate:
      metadata:
        annotations:
          k8s.v1.cni.cncf.io/networks: default/macvlan-conf-ens3f0
        labels:
          role: leader
      spec:
        containers:
          - command:
              - sh
              - '-c'
              - >
                export NCCL_IB_GID_INDEX=$(/host/rdma/show_gids | awk '$5 != ""
                && $6 == "v2" {print $3}') &&  bash
                /vllm-workspace/examples/online_serving/multi-node-serving.sh
                leader \
                  --ray_cluster_size=${LWS_GROUP_SIZE} \
                  --node-ip-address=${POD_IP}
                python3 -m vllm.entrypoints.openai.api_server \
                  --port 8080 \
                  --model /app/data/model/Qwen3-0.6B \
                  --served-model-name Qwen/Qwen3-0.6B \
                  --tensor-parallel-size 2 \
                  --pipeline-parallel-size 2
            env:
              - name: NCCL_IB_DISABLE
                value: '0'
              - name: NCCL_SOCKET_IFNAME
                value: net1
              - name: GLOO_SOCKET_IFNAME
                value: net1
              - name: TP_SOCKET_IFNAME
                value: net1
              - name: NCCL_DEBUG
                value: INFO
              - name: NCCL_IB_HCA
                value: mlx5_0:1
              - name: POD_IP
                valueFrom:
                  fieldRef:
                    fieldPath: status.podIP
            image: harbor.caih.local/caihcloud/vllm-openai:v0.8.5.post1
            name: vllm-leader
            ports:
              - containerPort: 8080
                protocol: TCP
            readinessProbe:
              initialDelaySeconds: 15
              periodSeconds: 10
              tcpSocket:
                port: 8080
            resources:
              limits:
                ephemeral-storage: 100Gi
                memory: 32Gi
                nvidia.com/gpu: '2'
                rdma/hca_shared_devices_ens3f0: '1'
              requests:
                cpu: '8'
                ephemeral-storage: 100Gi
                rdma/hca_shared_devices_ens3f0: '1'
            securityContext:
              capabilities:
                add:
                  - IPC_LOCK
            volumeMounts:
              - mountPath: /dev/shm
                name: dshm
              - mountPath: /app/data/model
                name: model-volume
              - mountPath: /host/rdma
                name: bin
        nodeSelector:
          gpu: 'on'
        volumes:
          - hostPath:
              path: /app/data/model
              type: Directory
            name: model-volume
          - hostPath:
              path: /sbin
              type: Directory
            name: bin
          - emptyDir:
              medium: Memory
            name: dshm
    restartPolicy: RecreateGroupOnPodRestart
    size: 2
    workerTemplate:
      metadata:
        annotations:
          k8s.v1.cni.cncf.io/networks: default/macvlan-conf-ens4f0
      spec:
        containers:
          - command:
              - sh
              - '-c'
              - >-
                export NCCL_IB_GID_INDEX=$(/host/rdma/show_gids | awk '$5 != ""
                && $6 == "v2" {print $3}') && bash
                /vllm-workspace/examples/online_serving/multi-node-serving.sh
                worker --ray_address=$(LWS_LEADER_ADDRESS)
            env:
              - name: NCCL_IB_DISABLE
                value: '0'
              - name: NCCL_SOCKET_IFNAME
                value: net1
              - name: GLOO_SOCKET_IFNAME
                value: net1
              - name: TP_SOCKET_IFNAME
                value: net1
              - name: NCCL_DEBUG
                value: INFO
              - name: NCCL_IB_HCA
                value: mlx5_0:1
            image: harbor.caih.local/caihcloud/vllm-openai:v0.8.5.post1
            name: vllm-worker
            resources:
              limits:
                ephemeral-storage: 100Gi
                memory: 32Gi
                nvidia.com/gpu: '2'
                rdma/hca_shared_devices_ens4f0: '1'
              requests:
                cpu: '8'
                ephemeral-storage: 100Gi
                rdma/hca_shared_devices_ens4f0: '1'
            securityContext:
              capabilities:
                add:
                  - IPC_LOCK
            volumeMounts:
              - mountPath: /dev/shm
                name: dshm
              - mountPath: /app/data/model
                name: model-volume
              - mountPath: /host/rdma
                name: bin
        nodeSelector:
          gpu: 'on'
        volumes:
          - hostPath:
              path: /app/data/model
              type: Directory
            name: model-volume
          - hostPath:
              path: /sbin
              type: Directory
            name: bin
          - emptyDir:
              medium: Memory
            name: dshm
  networkConfig:
    subdomainPolicy: Shared
  replicas: 1
  rolloutStrategy:
    rollingUpdateConfiguration:
      maxSurge: 0
      maxUnavailable: 1
    type: RollingUpdate
  startupPolicy: LeaderCreated
