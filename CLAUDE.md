# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Kubernetes infrastructure configuration project for deploying high-throughput LLM serving solutions (vLLM and SGLang) in GPU clusters. The project supports both single-node and distributed multi-node deployments using the LeaderWorkerSet pattern for coordinated distributed inference.

## Key Architecture

**Two Serving Frameworks:**
- **vLLM**: High-throughput LLM serving with Ray-based distributed computing
- **SGLang**: Structured generation language serving with tensor parallelism

**Deployment Patterns:**
- **LeaderWorkerSet (LWS)**: Coordinated multi-node deployments for distributed inference
- **Standard Deployment**: Single-node deployments for smaller models or development

**Resource Management:**
- GPU allocation per node (1-2 GPUs typical for test models like Qwen3-0.6B)
- Shared memory configuration for inter-process communication
- Host path volumes for model storage at `/app/data/model`

## Common Commands

### vLLM Operations
```bash
# Deploy multi-node vLLM with LeaderWorkerSet
kubectl apply -f vllm-lws.yaml

# Deploy single-node vLLM
kubectl apply -f vllm-deployment.yaml

# Check vLLM LeaderWorkerSet status
kubectl get leaderworkerset vllm -n vllm
kubectl get pods -l leaderworkerset.sigs.k8s.io/name=vllm -n vllm

# Access vLLM service
kubectl get svc vllm-leader -n vllm
kubectl port-forward svc/vllm-leader 8080:8080 -n vllm

# View vLLM logs
kubectl logs -l role=leader -n vllm
kubectl logs -l leaderworkerset.sigs.k8s.io/name=vllm -n vllm
```

### SGLang Operations
```bash
# Deploy multi-node SGLang with LeaderWorkerSet
kubectl apply -f sglang-lws.yaml

# Deploy single-node SGLang
kubectl apply -f sglang-deployment.yaml

# Check SGLang LeaderWorkerSet status
kubectl get leaderworkerset sglang -n sglang
kubectl get pods -l leaderworkerset.sigs.k8s.io/name=sglang -n sglang

# Access SGLang service
kubectl get svc sglang-leader -n sglang
kubectl port-forward svc/sglang-leader 40000:40000 -n sglang

# View SGLang logs
kubectl logs -l role=leader -n sglang
kubectl logs -l leaderworkerset.sigs.k8s.io/name=sglang -n sglang
```

### General Operations
```bash
# Clean up deployments
kubectl delete -f vllm-lws.yaml
kubectl delete -f sglang-lws.yaml
kubectl delete -f vllm-deployment.yaml
kubectl delete -f sglang-deployment.yaml

# Check all GPU nodes
kubectl get nodes -l gpu=on

# Monitor resource usage
kubectl top nodes
kubectl top pods -n vllm
kubectl top pods -n sglang
```

## Configuration Details

**vLLM Configuration:**
- Current model: Qwen3-0.6B (development/testing)
- Multi-node: tensor-parallel-size=2, pipeline-parallel-size=2 
- Single-node: tensor-parallel-size=2
- API port: 8080 (OpenAI-compatible)
- Image: `harbor.caih.local/caihcloud/vllm-openai:v0.8.5.post1`

**SGLang Configuration:**
- Current model: Qwen3-0.6B (development/testing)
- Multi-node: tensor-parallel-size=4 across nodes
- Single-node: tensor-parallel-size=1
- API port: 40000 (SGLang native API)
- Image: `harbor.caih.local/caihcloud/sglang:v0.4.6.post1-cu121`

**Networking Configuration:**
- NCCL networking optimizations for distributed inference
- InfiniBand disabled (`NCCL_IB_DISABLE=1`)
- Ethernet interface binding (`GLOO_SOCKET_IFNAME=eth0`)

## Important Considerations

- Models are currently configured for small test models (Qwen3-0.6B)
- Production deployments would use larger models requiring more GPUs
- HUGGING_FACE_HUB_TOKEN environment variable needs to be set for some models
- SGLang uses CUDA 12.1 with specific NCCL version constraints
- LeaderWorkerSet ensures coordinated restarts via `RecreateGroupOnPodRestart`
- Shared memory (`/dev/shm`) is critical for distributed inference performance
- Model files must be available at `/app/data/model` on all nodes