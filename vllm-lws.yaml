apiVersion: leaderworkerset.x-k8s.io/v1
kind: LeaderWorkerSet
metadata:
  name: vllm
  namespace: vllm # 根据实际情况修改
spec:
  replicas: 1
  leaderWorkerTemplate:
    size: 2
    restartPolicy: RecreateGroupOnPodRestart
    leaderTemplate:
      metadata:
        labels:
          role: leader
      spec:
        nodeSelector:
          gpu: "on"
        containers:
          - name: vllm-leader
            image: harbor.caih.local/caihcloud/vllm-openai:v0.8.5.post1
            command:
              - sh
              - -c
              - "bash /vllm-workspace/examples/online_serving/multi-node-serving.sh leader --ray_cluster_size=$(LWS_GROUP_SIZE); 
                 python3 -m vllm.entrypoints.openai.api_server --port 8080 --model /app/data/model/Qwen3-0.6B --served-model-name Qwen/Qwen3-0.6B --tensor-parallel-size 2 --pipeline-parallel-size 2"
            resources:
              limits:
                nvidia.com/gpu: "2"
                memory: 32Gi
                ephemeral-storage: 100Gi
              requests:
                ephemeral-storage: 100Gi
                cpu: 8
            ports:
              - containerPort: 8080
            readinessProbe:
              tcpSocket:
                port: 8080
              initialDelaySeconds: 15
              periodSeconds: 10
            volumeMounts:
              - mountPath: /dev/shm
                name: dshm
              - mountPath: /app/data/model
                name: model-volume
        volumes:
        - name: dshm
          emptyDir:
            medium: Memory
            sizeLimit: 4Gi
        - name: model-volume
          hostPath:
            path: /app/data/model
            type: Directory
    workerTemplate:
      spec:
        nodeSelector:
          gpu: "on"
        containers:
          - name: vllm-worker
            image: harbor.caih.local/caihcloud/vllm-openai:v0.8.5.post1
            command:
              - sh
              - -c
              - "bash /vllm-workspace/examples/online_serving/multi-node-serving.sh worker --ray_address=$(LWS_LEADER_ADDRESS)"
            resources:
              limits:
                nvidia.com/gpu: "2"
                memory: 32Gi
                ephemeral-storage: 100Gi
              requests:
                ephemeral-storage: 100Gi
                cpu: 8
            volumeMounts:
              - mountPath: /dev/shm
                name: dshm
              - mountPath: /app/data/model
                name: model-volume
        volumes:
        - name: dshm
          emptyDir:
            medium: Memory
            sizeLimit: 4Gi
        - name: model-volume
          hostPath:
            path: /app/data/model
            type: Directory
---
apiVersion: v1
kind: Service
metadata:
  name: vllm-leader
  namespace: vllm # 根据实际情况修改
spec:
  ports:
    - name: http
      port: 80
      protocol: TCP
      targetPort: 8080
  selector:
    leaderworkerset.sigs.k8s.io/name: vllm
    role: leader
  type: NodePort