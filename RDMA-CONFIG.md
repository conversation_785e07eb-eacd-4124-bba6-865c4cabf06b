# RDMA/InfiniBand 配置指南

本文档说明如何在 LLM 分布式推理中启用 RDMA/InfiniBand 支持。

## 前提条件

1. RDMA 环境已安装并配置完成
2. RoCE 网络接口名称为 `net1`
3. 容器需要访问 `/dev/infiniband` 设备

## 配置说明

### 关键环境变量

```yaml
# 启用 InfiniBand
- name: NCCL_IB_DISABLE
  value: '0'  # 设置为 0 启用 IB，设置为 1 禁用

# 指定网络接口
- name: NCCL_SOCKET_IFNAME
  value: net1  # 使用 RoCE 网络接口
- name: GLOO_SOCKET_IFNAME
  value: net1
- name: TP_SOCKET_IFNAME
  value: net1

# 指定 RDMA 设备
- name: NCCL_IB_HCA
  value: net1  # 指定要使用的 RDMA 设备

# 启用 GPUDirect RDMA
- name: NCCL_NET_GDR_LEVEL
  value: '2'  # 0: 禁用, 1: 仅接收, 2: 发送和接收

# RoCE 配置
- name: NCCL_IB_GID_INDEX
  value: '3'  # RoCE v2 通常使用 GID index 3

# 调试选项
- name: NCCL_DEBUG
  value: INFO  # 可选: TRACE, INFO, WARN, ERROR
```

### 必需的卷挂载

```yaml
volumes:
  - name: infiniband
    hostPath:
      path: /dev/infiniband
      type: Directory

volumeMounts:
  - mountPath: /dev/infiniband
    name: infiniband
```

### 安全上下文

```yaml
securityContext:
  capabilities:
    add:
      - IPC_LOCK  # RDMA 需要的内存锁定权限
```

## 使用方法

### vLLM 部署

```bash
# 部署启用 RDMA 的 vLLM
kubectl apply -f vllm-lws-rdma.yaml

# 检查部署状态
kubectl get leaderworkerset vllm -n vllm
kubectl get pods -l leaderworkerset.sigs.k8s.io/name=vllm -n vllm

# 查看日志验证 RDMA 是否正常工作
kubectl logs -l role=leader -n vllm | grep -i nccl
```

### SGLang 部署

```bash
# 部署启用 RDMA 的 SGLang
kubectl apply -f sglang-lws-rdma.yaml

# 检查部署状态
kubectl get leaderworkerset sglang -n sglang
kubectl get pods -l leaderworkerset.sigs.k8s.io/name=sglang -n sglang

# 查看日志验证 RDMA 是否正常工作
kubectl logs -l role=leader -n sglang | grep -i nccl
```

## 验证 RDMA 是否生效

1. 检查 NCCL 日志输出：
```bash
# 设置 NCCL_DEBUG=INFO 或 TRACE 后查看日志
kubectl logs <pod-name> -n <namespace> | grep -E "NCCL|IB|RDMA"
```

2. 预期看到类似输出：
```
NCCL INFO Using network IB
NCCL INFO Channel 00 : 0 -> 1 via NET/IB/0
NCCL INFO Using IB device mlx5_0 port 1
```

3. 如果 RDMA 未生效，会看到：
```
NCCL INFO Using network Socket
```

## 性能优化建议

1. **调整 NCCL 参数**：
   - `NCCL_IB_TIMEOUT`: 增加超时时间（默认 14）
   - `NCCL_IB_RETRY_CNT`: 增加重试次数（默认 7）
   - `NCCL_IB_SL`: 设置 Service Level（QoS）

2. **GPU 亲和性**：
   - 确保 GPU 和 RDMA 设备在同一 NUMA 节点
   - 使用 `NCCL_IB_CUDA_SUPPORT=1` 启用 CUDA 内存注册

3. **监控指标**：
   - 观察网络带宽利用率
   - 检查 RDMA 错误计数器
   - 监控 GPU 利用率和通信开销

## 故障排查

1. **权限问题**：
   ```bash
   # 检查容器是否有 IPC_LOCK 权限
   kubectl exec <pod-name> -n <namespace> -- capsh --print | grep cap_ipc_lock
   ```

2. **设备访问**：
   ```bash
   # 检查 /dev/infiniband 是否正确挂载
   kubectl exec <pod-name> -n <namespace> -- ls -la /dev/infiniband/
   ```

3. **网络接口**：
   ```bash
   # 验证 net1 接口是否存在
   kubectl exec <pod-name> -n <namespace> -- ip addr show net1
   ```

4. **RDMA 设备状态**：
   ```bash
   # 检查 RDMA 设备
   kubectl exec <pod-name> -n <namespace> -- ibv_devices
   kubectl exec <pod-name> -n <namespace> -- ibv_devinfo
   ```

## 注意事项

- SGLang 使用的 CUDA 12.1 版本对 NCCL 版本有限制（需要 2.11 以下）
- 确保所有节点的 RDMA 驱动版本一致
- RoCE 网络需要正确配置 PFC（Priority Flow Control）和 ECN（Explicit Congestion Notification）
- 大规模部署时考虑使用专用的 RDMA 网络以避免拥塞