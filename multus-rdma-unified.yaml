apiVersion: "k8s.cni.cncf.io/v1"
kind: NetworkAttachmentDefinition
metadata:
  name: rdma-macvlan
spec:
  config: |
    {
      "cniVersion": "0.3.1",
      "type": "macvlan",
      "ipam": {
        "type": "whereabouts",
        "range": "**********/17",
        "range_start": "**********",
        "range_end": "**************",
        "routes": [
           { "dst": "0.0.0.0/0" }
        ],
        "gateway": "**********"
      }
    }